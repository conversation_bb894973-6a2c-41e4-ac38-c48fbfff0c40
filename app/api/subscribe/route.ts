import { NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    // Create a Nodemailer transporter using Gmail
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    });

    // Email options
    const mailOptions = {
      from: `"KIH Newsletter" <${process.env.GMAIL_USER}>`,
      to: '<EMAIL>',
      subject: 'New Newsletter Subscription',
      text: `A new user has subscribed to your newsletter:
      
Email: ${email}

Subscription Date: ${new Date().toLocaleDateString()}
`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #0d9488;">New Newsletter Subscription</h2>
          <p>A new user has subscribed to your newsletter:</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Subscription Date:</strong> ${new Date().toLocaleDateString()}</p>
        </div>
      `,
    };

    // Send the email
    await transporter.sendMail(mailOptions);

    // Here you might want to store the email in a database as well
    // For example: await saveToDatabase(email);

    return NextResponse.json({ message: 'Subscription successful!' });
  } catch (error) {
    console.error('Error processing subscription:', error);
    return NextResponse.json(
      { error: 'Failed to process subscription' },
      { status: 500 }
    );
  }
}
