'use client'

import { motion } from 'framer-motion'
import { FileText, Scale, AlertTriangle, Shield, Users, Globe } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

export default function TermsOfService() {
  const sections = [
    {
      icon: Users,
      title: 'Acceptance of Terms',
      content: [
        'By accessing our website or services, you agree to be bound by these terms',
        'If you do not agree to these terms, please do not use our services',
        'We reserve the right to modify these terms at any time',
        'Continued use after changes constitutes acceptance of new terms'
      ]
    },
    {
      icon: Globe,
      title: 'Use of Services',
      content: [
        'Services are provided for legitimate business purposes only',
        'You must provide accurate and complete information',
        'You are responsible for maintaining confidentiality of your information',
        'Prohibited uses include illegal activities or misrepresentation'
      ]
    },
    {
      icon: Shield,
      title: 'Intellectual Property',
      content: [
        'All content on our website is owned by Kaiteur International Holdings',
        'You may not reproduce, distribute, or modify our content without permission',
        'Trademarks and logos are protected intellectual property',
        'User-generated content remains your property but grants us usage rights'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Disclaimers and Limitations',
      content: [
        'Services are provided "as is" without warranties of any kind',
        'We do not guarantee uninterrupted or error-free service',
        'Investment advice is general and not personalized recommendations',
        'Past performance does not guarantee future results'
      ]
    },
    {
      icon: Scale,
      title: 'Limitation of Liability',
      content: [
        'Our liability is limited to the maximum extent permitted by law',
        'We are not liable for indirect, incidental, or consequential damages',
        'Total liability shall not exceed the amount paid for our services',
        'Some jurisdictions do not allow limitation of liability'
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gray-900 text-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <FileText className="w-16 h-16 text-teal-400 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Terms of Service</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              These terms govern your use of Kaiteur International Holdings' website and services. Please read them carefully.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Last Updated */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-8"
        >
          <p className="text-orange-800 text-sm">
            <strong>Last Updated:</strong> January 1, 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Agreement to Terms</h2>
          <p className="text-gray-600 leading-relaxed mb-4">
            These Terms of Service ("Terms") constitute a legally binding agreement between you and Kaiteur International Holdings ("KIH," "we," "us," or "our") regarding your use of our website, services, and any related applications or platforms.
          </p>
          <p className="text-gray-600 leading-relaxed">
            By accessing or using our services, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy.
          </p>
        </motion.div>

        {/* Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <motion.div
              key={section.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                  <section.icon className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{section.title}</h3>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start">
                    <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-600 leading-relaxed">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Additional Terms */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.6 }}
          className="mt-12 bg-white rounded-xl shadow-sm border border-gray-100 p-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6">Governing Law and Dispute Resolution</h3>
          <div className="space-y-4 text-gray-600">
            <p>
              These Terms shall be governed by and construed in accordance with the laws of North Carolina, United States, without regard to its conflict of law provisions.
            </p>
            <p>
              Any disputes arising from these Terms or your use of our services shall be resolved through binding arbitration in Charlotte, North Carolina, except where prohibited by law.
            </p>
            <p>
              If any provision of these Terms is found to be unenforceable, the remaining provisions shall remain in full force and effect.
            </p>
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1, duration: 0.6 }}
          className="mt-8 bg-gray-50 rounded-xl p-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-4">Questions About These Terms</h3>
          <p className="text-gray-600 leading-relaxed mb-4">
            If you have any questions about these Terms of Service, please contact us:
          </p>
          <div className="space-y-2 text-gray-600">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> ************</p>
            <p><strong>Address:</strong> 4311 SchoolHouse Commons 129, Harrisburg, NC 28075</p>
          </div>
        </motion.div>

        {/* Back to Home */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="text-center mt-12"
        >
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-teal-600 to-orange-500 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300"
          >
            Back to Home
          </a>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
