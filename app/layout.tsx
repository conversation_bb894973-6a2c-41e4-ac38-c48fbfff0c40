import type { Metadata, Viewport } from 'next'
import './globals.css'

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export const metadata: Metadata = {
  title: 'Kaiteur International Holdings - Global Investment Leader',
  description: 'A global leader in investment and business development, creating sustainable value across diverse markets worldwide.',
  keywords: 'investment, holding company, global finance, business development, strategic investments, Kaiteur',
  authors: [{ name: 'Kaiteur International Holdings' }],
  creator: 'Kaiteur International Holdings',
  publisher: 'Kaiteur International Holdings',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://kaiteur-holdings.herokuapp.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Kaiteur International Holdings - Global Investment Leader',
    description: 'A global leader in investment and business development, creating sustainable value across diverse markets worldwide.',
    url: 'https://kaiteur-holdings.herokuapp.com',
    siteName: 'Kaiteur International Holdings',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Kaiteur International Holdings',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kaiteur International Holdings - Global Investment Leader',
    description: 'A global leader in investment and business development, creating sustainable value across diverse markets worldwide.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="font-sans">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0d9488" />
        <meta name="msapplication-TileColor" content="#0d9488" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        
        {/* Google Fonts */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        
        {/* Content Security Policy */}
        <meta
          httpEquiv="Content-Security-Policy"
          content={`
            default-src 'self';
            script-src 'self' 'unsafe-inline' 'unsafe-eval';
            style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
            style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com;
            img-src 'self' data: https:;
            font-src 'self' data: https://fonts.gstatic.com;
            connect-src 'self';
            frame-src 'none';
            object-src 'none';
          `.replace(/\n/g, ' ')}
        />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <div className="min-h-screen bg-gradient-to-br from-teal-50 via-orange-50 to-yellow-50">
          <a
            href="#main-content"
            className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-teal text-white px-4 py-2 rounded-md z-50"
          >
            Skip to main content
          </a>
          {children}
        </div>
      </body>
    </html>
  )
}
