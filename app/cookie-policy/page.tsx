'use client'

import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Setting<PERSON>, BarChart3, Shield, Users, Globe } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

export default function CookiePolicy() {
  const cookieTypes = [
    {
      icon: Shield,
      title: 'Essential Cookies',
      description: 'Required for basic website functionality',
      examples: [
        'Session management and security',
        'Form submission and validation',
        'Basic website navigation',
        'User authentication (if applicable)'
      ],
      canDisable: false
    },
    {
      icon: BarChart3,
      title: 'Analytics Cookies',
      description: 'Help us understand how visitors use our website',
      examples: [
        'Google Analytics for traffic analysis',
        'Page view and user behavior tracking',
        'Performance monitoring',
        'Website optimization data'
      ],
      canDisable: true
    },
    {
      icon: Settings,
      title: 'Functional Cookies',
      description: 'Enhance your experience with additional features',
      examples: [
        'Language and region preferences',
        'Customized content display',
        'Remember your choices',
        'Improved user interface'
      ],
      canDisable: true
    },
    {
      icon: Users,
      title: 'Marketing Cookies',
      description: 'Used to deliver relevant content and advertisements',
      examples: [
        'Social media integration',
        'Targeted content delivery',
        'Newsletter preferences',
        'Third-party advertising (if applicable)'
      ],
      canDisable: true
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gray-900 text-white py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Cookie className="w-16 h-16 text-teal-400 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Cookie Policy</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Learn about how Kaiteur International Holdings uses cookies and similar technologies on our website.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Last Updated */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8"
        >
          <p className="text-blue-800 text-sm">
            <strong>Last Updated:</strong> January 1, 2024
          </p>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">What Are Cookies?</h2>
          <p className="text-gray-600 leading-relaxed mb-4">
            Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better browsing experience by remembering your preferences and analyzing how you use our site.
          </p>
          <p className="text-gray-600 leading-relaxed">
            This Cookie Policy explains what cookies we use, why we use them, and how you can manage your cookie preferences.
          </p>
        </motion.div>

        {/* Cookie Types */}
        <div className="space-y-8 mb-12">
          <h2 className="text-2xl font-bold text-gray-900">Types of Cookies We Use</h2>
          {cookieTypes.map((type, index) => (
            <motion.div
              key={type.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8"
            >
              <div className="flex items-start justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <type.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{type.title}</h3>
                    <p className="text-gray-600">{type.description}</p>
                  </div>
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  type.canDisable 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {type.canDisable ? 'Optional' : 'Required'}
                </div>
              </div>
              <ul className="space-y-2">
                {type.examples.map((example, exampleIndex) => (
                  <li key={exampleIndex} className="flex items-start">
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-600">{example}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Managing Cookies */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6">Managing Your Cookie Preferences</h3>
          <div className="space-y-4 text-gray-600">
            <p>
              You have several options for managing cookies on our website:
            </p>
            <ul className="space-y-3 ml-4">
              <li className="flex items-start">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span><strong>Browser Settings:</strong> Most browsers allow you to control cookies through their settings. You can block or delete cookies, but this may affect website functionality.</span>
              </li>
              <li className="flex items-start">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span><strong>Opt-Out Tools:</strong> You can opt out of analytics cookies using tools like Google Analytics Opt-out Browser Add-on.</span>
              </li>
              <li className="flex items-start">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span><strong>Do Not Track:</strong> We respect Do Not Track signals where technically feasible.</span>
              </li>
            </ul>
          </div>
        </motion.div>

        {/* Third-Party Cookies */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.6 }}
          className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 mb-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-6">Third-Party Cookies</h3>
          <p className="text-gray-600 leading-relaxed mb-4">
            Our website may include third-party services that set their own cookies. These include:
          </p>
          <ul className="space-y-2 text-gray-600">
            <li className="flex items-start">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span><strong>Google Analytics:</strong> For website analytics and performance monitoring</span>
            </li>
            <li className="flex items-start">
              <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
              <span><strong>Social Media Platforms:</strong> For social sharing and integration features</span>
            </li>
          </ul>
          <p className="text-gray-600 leading-relaxed mt-4">
            These third parties have their own privacy policies and cookie practices, which we encourage you to review.
          </p>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.6 }}
          className="bg-gray-50 rounded-xl p-8"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-4">Questions About Cookies</h3>
          <p className="text-gray-600 leading-relaxed mb-4">
            If you have any questions about our use of cookies, please contact us:
          </p>
          <div className="space-y-2 text-gray-600">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> ************</p>
            <p><strong>Address:</strong> 4311 SchoolHouse Commons 129, Harrisburg, NC 28075</p>
          </div>
        </motion.div>

        {/* Back to Home */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="text-center mt-12"
        >
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-teal-600 to-orange-500 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300"
          >
            Back to Home
          </a>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
