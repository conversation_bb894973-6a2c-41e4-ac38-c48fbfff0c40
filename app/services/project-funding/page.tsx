'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Heart, Globe, Users, Target } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function ProjectFunding() {
  const features = [
    {
      icon: Heart,
      title: 'Non-Profit Support',
      description: 'Dedicated funding programs for non-profit organizations making a difference in their communities.'
    },
    {
      icon: Users,
      title: 'Low-Income Initiatives',
      description: 'Specialized financial solutions for projects serving low-income and underserved populations.'
    },
    {
      icon: Target,
      title: 'Project Structuring',
      description: 'Expert guidance in structuring projects for maximum impact and sustainable funding.'
    },
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'International funding opportunities for projects across multiple continents.'
    }
  ]

  const benefits = [
    'Flexible funding terms tailored to project needs',
    'Competitive interest rates for qualifying projects',
    'Expert consultation throughout the funding process',
    'Long-term partnership and ongoing support',
    'Fast approval process for urgent initiatives',
    'Comprehensive risk assessment and mitigation'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <TrendingUp className="w-16 h-16 text-white/90 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Project Funding</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Providing funding for marginalized communities worldwide through prudent development and creative financing solutions.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Empowering Communities Through Strategic Funding</h2>
          <p className="text-lg text-teal-600 leading-relaxed max-w-4xl mx-auto text-center">
            At Kaiteur International Holdings, we believe in the power of strategic funding to transform communities and create lasting positive impact. Our project funding services are designed to support initiatives that serve marginalized communities and address critical social needs worldwide.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-teal-50 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-teal-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Benefits Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose Our Project Funding?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-teal-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-teal-600">{benefit}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Process Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Funding Process</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { step: '01', title: 'Initial Consultation', description: 'We discuss your project goals, funding needs, and impact objectives.' },
              { step: '02', title: 'Project Assessment', description: 'Our team evaluates the project viability, community impact, and funding structure.' },
              { step: '03', title: 'Funding & Support', description: 'We provide funding and ongoing support to ensure project success and sustainability.' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-teal-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Fund Your Project?</h3>
          <p className="text-lg mb-6 text-white/90">
            Let&apos;s discuss how we can help bring your community-focused project to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#contact"
              className="px-8 py-3 bg-white text-teal-600 font-semibold rounded-lg hover:bg-teal-50 transition-colors duration-300"
            >
              Get Started Today
            </Link>
            <Link
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-teal-600 transition-all duration-300"
            >
              Learn More
            </Link>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
