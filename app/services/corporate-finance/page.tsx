'use client'

import { motion } from 'framer-motion'
import { DollarSign, TrendingUp, PieChart, Calculator, BarChart3, Shield } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function CorporateFinance() {
  const features = [
    {
      icon: Calculator,
      title: 'Financial Planning & Analysis',
      description: 'Comprehensive financial planning, budgeting, and analysis to optimize your capital structure and cash flow.'
    },
    {
      icon: TrendingUp,
      title: 'Capital Raising',
      description: 'Expert assistance in raising debt and equity capital through various financing instruments and investor networks.'
    },
    {
      icon: PieChart,
      title: 'Capital Structure Optimization',
      description: 'Strategic optimization of debt-to-equity ratios and capital allocation to maximize shareholder value.'
    },
    {
      icon: Shield,
      title: 'Risk Management',
      description: 'Comprehensive financial risk assessment and mitigation strategies to protect your business interests.'
    }
  ]

  const services = [
    'Debt and Equity Financing',
    'Working Capital Management',
    'Cash Flow Optimization',
    'Financial Restructuring',
    'Treasury Management',
    'Investment Banking Services'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <DollarSign className="w-16 h-16 text-white/90 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Corporate Finance</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Comprehensive corporate finance solutions to optimize capital structure, manage financial risk, and drive sustainable growth.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Strategic Financial Solutions</h2>
          <p className="text-lg text-teal-600 leading-relaxed max-w-4xl mx-auto text-center">
            Our corporate finance team provides sophisticated financial solutions that help businesses optimize their capital structure, manage risk, and achieve their strategic objectives. We combine deep financial expertise with practical business insights to deliver results.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-teal-50 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-teal-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Services Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Finance Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-teal-600">{service}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Finance Solutions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Finance Approach</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Strategic Analysis', 
                description: 'Comprehensive financial analysis to identify opportunities and optimize capital allocation.',
                icon: BarChart3
              },
              { 
                title: 'Tailored Solutions', 
                description: 'Customized financing solutions that align with your business objectives and risk profile.',
                icon: PieChart
              },
              { 
                title: 'Ongoing Support', 
                description: 'Continuous monitoring and advisory services to ensure optimal financial performance.',
                icon: TrendingUp
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-600 to-orange-500 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="w-8 h-8" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-teal-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Financing Options */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.85, duration: 0.8 }}
          className="bg-gray-50 rounded-2xl p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Financing Solutions We Provide</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { title: 'Debt Financing', description: 'Traditional and alternative debt solutions for growth and operations' },
              { title: 'Equity Financing', description: 'Private equity and venture capital connections for expansion' },
              { title: 'Mezzanine Financing', description: 'Hybrid financing solutions combining debt and equity features' },
              { title: 'Project Financing', description: 'Specialized financing for large-scale projects and developments' },
              { title: 'Trade Financing', description: 'International trade finance and working capital solutions' },
              { title: 'Restructuring', description: 'Financial restructuring and turnaround advisory services' }
            ].map((item, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-teal-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Value Proposition */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose Our Corporate Finance Services?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { title: 'Expert Team', description: 'Experienced finance professionals with deep industry knowledge' },
              { title: 'Global Network', description: 'Extensive network of investors, lenders, and financial institutions' },
              { title: 'Proven Results', description: 'Track record of successful financing transactions and outcomes' },
              { title: 'Tailored Approach', description: 'Customized solutions that fit your specific business needs' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-teal-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Optimize Your Corporate Finance?</h3>
          <p className="text-lg mb-6 text-white/90">
            Let our corporate finance experts help you achieve your financial objectives and drive growth.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#contact"
              className="px-8 py-3 bg-white text-teal-600 font-semibold rounded-lg hover:bg-teal-50 transition-colors duration-300"
            >
              Discuss Your Needs
            </Link>
            <Link
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-teal-600 transition-all duration-300"
            >
              Learn More
            </Link>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
