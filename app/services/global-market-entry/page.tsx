'use client'

import { motion } from 'framer-motion'
import { Globe, Map, Users, Target, BarChart3, Shield } from 'lucide-react'

export default function GlobalMarketEntry() {
  const features = [
    {
      icon: Map,
      title: 'Market Research & Analysis',
      description: 'Comprehensive market research to identify opportunities, assess competition, and understand local dynamics.'
    },
    {
      icon: Target,
      title: 'Entry Strategy Development',
      description: 'Customized market entry strategies tailored to your business model and target market characteristics.'
    },
    {
      icon: Users,
      title: 'Local Partnership Development',
      description: 'Identifying and establishing strategic partnerships with local businesses and stakeholders.'
    },
    {
      icon: Shield,
      title: 'Regulatory Compliance',
      description: 'Navigating complex regulatory environments and ensuring full compliance with local laws and regulations.'
    }
  ]

  const markets = [
    'North America (USA, Canada)',
    'Europe (UK, Germany, France)',
    'Africa (Multiple Markets)',
    'Caribbean (Dominican Republic)',
    'Asia-Pacific (Emerging Markets)',
    'Latin America (Growth Markets)'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-emerald-600 to-emerald-800 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Globe className="w-16 h-16 text-emerald-200 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Global Market Entry</h1>
            <p className="text-xl text-emerald-100 max-w-3xl mx-auto">
              Expert guidance for expanding your business into new international markets with strategic planning and local expertise.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Expanding Your Global Footprint</h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto text-center">
            With offices in Charlotte, London, Africa, and the Dominican Republic, Kaiteur International Holdings provides unparalleled expertise in global market entry. We help businesses navigate the complexities of international expansion with confidence and success.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-emerald-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Global Presence */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Global Market Coverage</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {markets.map((market, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-emerald-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-gray-600">{market}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Entry Process */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Market Entry Process</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { step: '01', title: 'Market Assessment', description: 'Comprehensive analysis of target market opportunities and challenges' },
              { step: '02', title: 'Strategy Development', description: 'Customized entry strategy based on market insights and business goals' },
              { step: '03', title: 'Implementation', description: 'Execution of market entry plan with local support and guidance' },
              { step: '04', title: 'Optimization', description: 'Ongoing monitoring and optimization for sustainable growth' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Success Factors */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.85, duration: 0.8 }}
          className="bg-gray-50 rounded-2xl p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Key Success Factors</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Local Expertise', 
                description: 'Deep understanding of local markets, culture, and business practices through our global offices.',
                icon: Users
              },
              { 
                title: 'Data-Driven Approach', 
                description: 'Comprehensive market analysis and data-driven insights to inform strategic decisions.',
                icon: BarChart3
              },
              { 
                title: 'Risk Management', 
                description: 'Proactive identification and mitigation of market entry risks and challenges.',
                icon: Shield
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="w-8 h-8" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Global Offices */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Global Office Network</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { location: 'Charlotte, NC', region: 'North America HQ', description: 'Strategic planning and coordination' },
              { location: 'London, UK', region: 'European Operations', description: 'European market expertise' },
              { location: 'Africa', region: 'African Markets', description: 'Emerging market opportunities' },
              { location: 'Dominican Republic', region: 'Caribbean Hub', description: 'Latin American expansion' }
            ].map((office, index) => (
              <div key={index} className="text-center">
                <h4 className="text-lg font-semibold text-gray-900 mb-1">{office.location}</h4>
                <p className="text-emerald-600 font-medium text-sm mb-2">{office.region}</p>
                <p className="text-gray-600 text-sm">{office.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.8 }}
          className="bg-gradient-to-r from-emerald-600 to-teal-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Go Global?</h3>
          <p className="text-lg mb-6 text-emerald-100">
            Let our global market entry experts help you expand into new international markets successfully.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/#contact"
              className="px-8 py-3 bg-white text-emerald-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300"
            >
              Start Your Global Expansion
            </a>
            <a
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-emerald-600 transition-all duration-300"
            >
              Learn More
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
