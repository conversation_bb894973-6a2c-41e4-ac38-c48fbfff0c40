'use client'

import { motion } from 'framer-motion'
import { Briefcase, Rocket, Users, Target, TrendingUp } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function BusinessDevelopment() {
  const features = [
    {
      icon: Rocket,
      title: 'New Business Development',
      description: 'Comprehensive support for launching new ventures, from concept development to market entry strategies.'
    },
    {
      icon: Target,
      title: 'Market Entry Strategy',
      description: 'Strategic planning and execution for entering new markets, both domestically and internationally.'
    },
    {
      icon: Users,
      title: 'Partnership Development',
      description: 'Building strategic partnerships and alliances to accelerate business growth and market penetration.'
    },
    {
      icon: TrendingUp,
      title: 'Growth Optimization',
      description: 'Identifying and implementing growth opportunities to scale your business efficiently and sustainably.'
    }
  ]

  const services = [
    'Business Plan Development',
    'Market Research and Analysis',
    'Strategic Partnership Formation',
    'Operational Efficiency Consulting',
    'Revenue Stream Optimization',
    'Scalability Planning and Implementation'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-gray-50">
      <Navigation />
      {/* <PERSON>er */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Briefcase className="w-16 h-16 text-teal-100 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Business Development</h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Comprehensive business development solutions to help new and growing businesses achieve sustainable success and market leadership.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Accelerating Business Growth</h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto text-center">
            At Kaiteur International Holdings, we understand that successful business development requires more than just capital—it requires strategic vision, market expertise, and operational excellence. Our comprehensive approach helps businesses at every stage of their journey.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-teal-50 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Services Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Development Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-gray-600">{service}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Development Process */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Development Process</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { step: '01', title: 'Assessment', description: 'Comprehensive analysis of your business, market position, and growth potential.' },
              { step: '02', title: 'Strategy', description: 'Development of customized growth strategies aligned with your business objectives.' },
              { step: '03', title: 'Implementation', description: 'Execution of strategic initiatives with ongoing support and guidance.' },
              { step: '04', title: 'Optimization', description: 'Continuous monitoring and optimization to ensure sustainable growth.' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {item.step}
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Success Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.85, duration: 0.8 }}
          className="bg-gray-50 rounded-2xl p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose Our Business Development Services?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { metric: '25+', label: 'Years of Experience', description: 'Proven track record in business development across multiple industries' },
              { metric: '20+', label: 'Successful Projects', description: 'Helping businesses achieve their growth objectives worldwide' },
              { metric: '4', label: 'Global Offices', description: 'International presence in key markets for global business development coming soon' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-teal-600 mb-2">{item.metric}</div>
                <div className="text-lg font-semibold text-gray-900 mb-2">{item.label}</div>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-gradient-to-r from-orange-600 to-teal-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Accelerate Your Business Growth?</h3>
          <p className="text-lg mb-6 text-white/90">
            Let our experienced team help you develop and execute strategies for sustainable business success.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#contact"
              className="px-8 py-3 bg-white text-teal-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300"
            >
              Start Your Journey
            </Link>
            <Link
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-teal-600 transition-all duration-300"
            >
              Learn More
            </Link>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
