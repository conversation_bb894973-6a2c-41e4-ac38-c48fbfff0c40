'use client'

import { motion } from 'framer-motion'
import { TrendingUp, PieChart, Shield, Globe, Target, BarChart3 } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'

export default function InvestmentManagement() {
  const features = [
    {
      icon: Pie<PERSON>hart,
      title: 'Portfolio Diversification',
      description: 'Strategic asset allocation across multiple sectors and geographic regions to minimize risk and maximize returns.'
    },
    {
      icon: Shield,
      title: 'Risk Management',
      description: 'Comprehensive risk assessment and mitigation strategies to protect your investments in volatile markets.'
    },
    {
      icon: Target,
      title: 'Strategic Planning',
      description: 'Long-term investment strategies aligned with your financial goals and risk tolerance.'
    },
    {
      icon: BarChart3,
      title: 'Performance Monitoring',
      description: 'Continuous monitoring and analysis of investment performance with regular reporting and adjustments.'
    }
  ]

  const services = [
    'Institutional Investment Management',
    'Private Wealth Management',
    'Alternative Investment Strategies',
    'ESG and Impact Investing',
    'Real Estate Investment Trusts (REITs)',
    'Commodity and Asset Trading'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-600 to-teal-800 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <TrendingUp className="w-16 h-16 text-teal-200 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Investment Management</h1>
            <p className="text-xl text-teal-100 max-w-3xl mx-auto">
              Professional investment management services designed to grow and protect your wealth through strategic portfolio management and expert market analysis.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Expert Investment Solutions</h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto text-center">
            With over 25 years of experience in global markets, Kaiteur International Holdings provides sophisticated investment management services that combine traditional wisdom with innovative strategies. Our approach focuses on sustainable growth while managing risk across diverse market conditions.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-teal-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Services Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Investment Services</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-teal-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-gray-600">{service}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Investment Philosophy */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Investment Philosophy</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Long-Term Focus', 
                description: 'We believe in the power of long-term investing, focusing on sustainable growth rather than short-term gains.',
                icon: Target
              },
              { 
                title: 'Global Perspective', 
                description: 'Our international experience allows us to identify opportunities across diverse markets and economies.',
                icon: Globe
              },
              { 
                title: 'Risk-Adjusted Returns', 
                description: 'We prioritize risk-adjusted returns, ensuring that every investment decision considers both potential and protection.',
                icon: Shield
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="w-8 h-8" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Grow Your Wealth?</h3>
          <p className="text-lg mb-6 text-teal-100">
            Let our experienced investment team help you achieve your financial goals.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/#contact"
              className="px-8 py-3 bg-white text-teal-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300"
            >
              Schedule Consultation
            </a>
            <a
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-teal-600 transition-all duration-300"
            >
              Learn More
            </a>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
