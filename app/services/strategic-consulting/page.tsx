'use client'

import { motion } from 'framer-motion'
import { Lightbulb, Target, Users, BarChart3, Globe, Zap } from 'lucide-react'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import Link from 'next/link'

export default function StrategicConsulting() {
  const features = [
    {
      icon: Target,
      title: 'Strategic Planning',
      description: 'Comprehensive strategic planning services to define clear objectives and actionable roadmaps for success.'
    },
    {
      icon: BarChart3,
      title: 'Performance Analysis',
      description: 'In-depth analysis of business performance with data-driven insights and improvement recommendations.'
    },
    {
      icon: Users,
      title: 'Organizational Development',
      description: 'Optimizing organizational structure, culture, and processes to enhance efficiency and effectiveness.'
    },
    {
      icon: Zap,
      title: 'Innovation Strategy',
      description: 'Developing innovation frameworks and strategies to maintain competitive advantage in evolving markets.'
    }
  ]

  const consultingAreas = [
    'Corporate Strategy Development',
    'Digital Transformation',
    'Operational Excellence',
    'Change Management',
    'Market Positioning',
    'Competitive Analysis'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-teal-50">
      <Navigation />
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 text-white py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <Lightbulb className="w-16 h-16 text-purple-200 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Strategic Consulting</h1>
            <p className="text-xl text-purple-100 max-w-3xl mx-auto">
              Expert strategic consulting services to help organizations navigate complex challenges and achieve sustainable competitive advantage.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Overview */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.8 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-6 text-center">Transforming Vision into Reality</h2>
          <p className="text-lg text-gray-600 leading-relaxed max-w-4xl mx-auto text-center">
            Our strategic consulting services combine deep industry expertise with innovative thinking to help organizations overcome challenges, seize opportunities, and achieve their most ambitious goals. We work as trusted partners to deliver sustainable results.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.6 }}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <feature.icon className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="text-xl font-bold text-gray-900">{feature.title}</h3>
              </div>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Consulting Areas */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7, duration: 0.8 }}
          className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Our Consulting Expertise</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {consultingAreas.map((area, index) => (
              <div key={index} className="flex items-start">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <span className="text-gray-600">{area}</span>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Consulting Approach */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          className="mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Consulting Approach</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'Collaborative Partnership', 
                description: 'We work closely with your team, combining our expertise with your industry knowledge for optimal results.',
                icon: Users
              },
              { 
                title: 'Data-Driven Insights', 
                description: 'Our recommendations are backed by thorough analysis and market intelligence for informed decision-making.',
                icon: BarChart3
              },
              { 
                title: 'Global Perspective', 
                description: 'International experience across diverse markets provides unique insights and best practices.',
                icon: Globe
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center mx-auto mb-4">
                  <item.icon className="w-8 h-8" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Value Proposition */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.85, duration: 0.8 }}
          className="bg-gray-50 rounded-2xl p-8 mb-16"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Why Choose Our Strategic Consulting?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { title: 'Proven Results', description: 'Track record of successful strategic transformations' },
              { title: 'Industry Expertise', description: 'Deep knowledge across multiple sectors and markets' },
              { title: 'Tailored Solutions', description: 'Customized strategies that fit your unique context' },
              { title: 'Implementation Support', description: 'Ongoing support to ensure successful execution' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{item.title}</h4>
                <p className="text-gray-600 text-sm">{item.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.8 }}
          className="bg-gradient-to-r from-purple-600 to-teal-500 rounded-2xl p-8 text-center text-white"
        >
          <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Strategy?</h3>
          <p className="text-lg mb-6 text-purple-100">
            Let our strategic consulting experts help you navigate challenges and unlock new opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/#contact"
              className="px-8 py-3 bg-white text-purple-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors duration-300"
            >
              Get Strategic Guidance
            </a>
            <Link
              href="/"
              className="px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              Learn More
            </Link>
          </div>
        </motion.div>
      </div>
      <Footer />
    </div>
  )
}
