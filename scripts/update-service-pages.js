const fs = require('fs').promises;
const path = require('path');

const servicePages = [
  'business-development',
  'corporate-finance',
  'global-market-entry',
  'investment-management',
  'mergers-acquisitions',
  'project-funding',
  'strategic-consulting'
];

const updateFile = async (filePath) => {
  try {
    let content = await fs.readFile(filePath, 'utf8');
    
    // Update background gradient
    content = content.replace(
      /bg-gradient-to-br from-slate-50 via-white to-teal-50/g,
      'bg-gradient-to-br from-white to-gray-50'
    );
    
    // Update header gradient
    content = content.replace(
      /bg-gradient-to-r from-orange-600 to-orange-800/g,
      'bg-gradient-to-r from-teal-600 to-orange-500'
    );
    
    // Update icon colors
    content = content.replace(
      /text-orange-200/g,
      'text-teal-100'
    );
    
    // Update text colors
    content = content.replace(
      /text-orange-100/g,
      'text-white/90'
    );
    
    // Update icon backgrounds
    content = content.replace(
      /bg-orange-100/g,
      'bg-teal-50'
    );
    
    // Update icon colors
    content = content.replace(
      /text-orange-600/g,
      'text-teal-600'
    );
    
    await fs.writeFile(filePath, content, 'utf8');
    console.log(`Updated: ${filePath}`);
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error);
  }
};

const updateAllServicePages = async () => {
  const basePath = path.join(process.cwd(), 'app/services');
  
  for (const page of servicePages) {
    const filePath = path.join(basePath, page, 'page.tsx');
    await updateFile(filePath);
  }
  
  console.log('All service pages have been updated!');};

updateAllServicePages().catch(console.error);
