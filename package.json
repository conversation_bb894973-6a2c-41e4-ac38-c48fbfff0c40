{"name": "kaiteur-holdings", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start -p $PORT", "lint": "next lint", "postinstall": "npm run build", "heroku-postbuild": "npm run build"}, "dependencies": {"@heroicons/react": "^2.1.1", "@types/nodemailer": "^6.4.17", "clsx": "^2.1.1", "framer-motion": "^10.18.0", "lucide-react": "^0.460.0", "next": "^14.2.30", "nodemailer": "^6.10.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@types/node": "^20.11.25", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "resolutions": {"webpack": "^5.89.0"}}