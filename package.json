{"name": "kaiteur-holdings", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p $PORT", "lint": "next lint", "heroku-postbuild": "npm run build"}, "dependencies": {"@formspree/react": "^3.0.0", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "lucide-react": "^0.460.0", "next": "^15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/node": "^20.17.50", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^9.15.0", "eslint-config-next": "^15.0.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}