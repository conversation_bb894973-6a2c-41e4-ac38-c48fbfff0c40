'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Building, Users, Target, Briefcase } from 'lucide-react'
import Image from 'next/image'

const Services = () => {
  const services = [
    {
      icon: TrendingUp,
      title: 'Project Funding',
      description: 'Providing funding for marginalized communities worldwide through prudent development and creative financing.',
      features: ['Non-Profit Support', 'Low-Income Initiatives', 'Project Structuring', 'Global Reach'],
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Building,
      title: 'Real Estate Development',
      description: 'Sustainable development projects that create jobs and improve communities.',
      features: ['Affordable Housing', 'Commercial Development', 'Urban Renewal', 'Sustainable Design', 'Visa/Residency Experts'],
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Mergers & Acquisitions',
      description: 'Strategic acquisitions to enhance business value and market position.',
      features: ['Due Diligence', 'Valuation Services', 'Deal Structuring', 'Integration Support'],
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Target,
      title: 'Humanitarian Initiatives',
      description: 'Projects focused on creating jobs, clean water, and solving housing problems.',
      features: ['Job Creation', 'Water Solutions', 'Housing Programs', 'Environmental Stewardship'],
      gradient: 'from-teal-light to-teal'
    },
    {
      icon: Briefcase,
      title: 'Business Development',
      description: 'Comprehensive solutions for new and growing businesses.',
      features: ['New Business Development', 'Market Entry Strategy', 'Business Planning', 'Operational Support'],
      gradient: 'from-purple-500 to-purple-700'
    },
    {
      icon: Briefcase,
      title: 'Commodities & Assets',
      description: 'Expert management and trading of commodities and business assets.',
      features: ['Commodities Trading', 'Asset Management', 'Portfolio Optimization', 'Strategic Planning'],
      gradient: 'from-indigo-500 to-indigo-700'
    }
  ]

  return (
    <section id="services" className="py-24 relative overflow-hidden bg-white">
      {/* Subtle Gradient Background */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-teal-500 to-orange-500"></div>
        <div className="absolute top-1/4 -left-40 w-96 h-96 bg-teal-50/50 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 -right-40 w-96 h-96 bg-orange-50/50 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-center text-black mb-4">Our Services</h2>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-orange-600 mx-auto mb-6"></div>
          <p className="text-xl text-center text-gray-800 max-w-3xl mx-auto mb-12">KIH is dedicated to empowering marginalized communities worldwide through strategic humanitarian investments. We provide comprehensive support and innovative financing solutions to drive sustainable development and create lasting impact.</p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="p-6 bg-white/10 backdrop-blur-sm rounded-xl shadow-lg h-full flex flex-col border border-white/20 text-black">
                {/* Background Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-teal-400/20 to-orange-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Icon */}
                <div className={`w-14 h-14 rounded-xl flex items-center justify-center mb-6 text-white ${
                      service.title === 'Project Funding' || service.title === 'Humanitarian Initiatives' 
                        ? 'bg-teal-500' 
                        : service.title === 'Real Estate Development' || service.title === 'Mergers & Acquisitions'
                        ? 'bg-orange-500'
                        : 'bg-purple-500'
                    } shadow-md group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="w-6 h-6" />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold text-gray-800 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>

                  {/* Features */}
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <motion.li
                        key={feature}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: (index * 0.1) + (featureIndex * 0.05), duration: 0.4 }}
                        viewport={{ once: true }}
                        className="flex items-center text-gray-600 mb-2"
                      >
                        <span className="w-1.5 h-1.5 bg-teal-500 rounded-full mr-2"></span>
                        <span className="text-gray-800">{feature}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Legacy Aces Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="p-8 rounded-xl bg-white border border-gray-100 hover:border-teal-100 hover:bg-gray-50/50 hover:shadow-md transition-all duration-300 h-full flex flex-col group">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(245,158,11,0.1)_0%,transparent_70%)]"></div>

            <h3 className="text-3xl font-bold text-gray-800 mb-6 text-center relative z-10">
              Our Consortium: Legacy Aces
            </h3>
            <p className="text-gray-700 mb-12 text-lg text-center max-w-4xl mx-auto relative z-10">
              Kaiteur International Holdings, LLC (KIH) is a dynamic conglomerate with a global network of partners. Our consortium of specialized companies works in harmony to advance our humanitarian mission, combining expertise in various sectors to create comprehensive solutions for underserved communities worldwide.
            </p>
            
            {/* Legacy Aces - Centered Above Others */}
            <div className="flex justify-center mb-12">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex flex-col items-center"
              >
                <div className="w-48 h-48 rounded-full bg-white shadow-lg border-4 border-gold overflow-hidden mb-6 hover:shadow-xl transition-all duration-300 p-1 flex items-center justify-center relative">
                  <div className="absolute inset-0 bg-white z-0"></div>
                  <div className="w-[calc(100%-0.5rem)] h-[calc(100%-0.5rem)] rounded-full overflow-hidden bg-white flex items-center justify-center relative z-10">
                    <div className="relative w-full h-full">
                      <Image
                        src="/businesslogos/legacyaces.png"
                        alt="Legacy Aces"
                        fill
                        style={{ objectFit: 'contain' }}
                        className="p-3"
                        priority
                      />
                    </div>
                  </div>
                </div>
                <span className="text-gray-800 text-xl font-semibold text-center">Legacy Aces</span>
              </motion.div>
            </div>

            {/* Consortium Members Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-8">
              {[
                { name: 'Connoisseur', logo: '/businesslogos/connoisseur.png' },
                { name: 'Boriqn', logo: '/businesslogos/boriqn.png' },
                { name: 'KEP', logo: '/businesslogos/kep.png' },
                { name: 'Eighty-Three 50', logo: '/businesslogos/eightythree50.png' },
                { name: 'PierWater International', logo: '/businesslogos/pwi.png' },
                { name: 'Kingdom City', logo: '/businesslogos/kingdom.png' },
                { name: 'Astrid Capital', logo: '/businesslogos/astrid.png' },
                { name: 'Lemuria', logo: '/businesslogos/lemuria.png' }
              ].map((company, index) => (
                <motion.div
                  key={company.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center group"
                >
                  <div className="w-48 h-48 rounded-full bg-white/5 backdrop-blur-sm border-4 border-teal-500/40 flex items-center justify-center overflow-hidden transition-all duration-300 mb-4 hover:shadow-lg hover:scale-105 hover:border-teal-500/60 hover:bg-white/10 p-1">
                    {company.logo ? (
                      <div className="w-[calc(100%-0.5rem)] h-[calc(100%-0.5rem)] rounded-full overflow-hidden bg-white flex items-center justify-center">
                        <div className="relative w-full h-full">
                          <Image
                            src={company.logo}
                            alt={company.name}
                            fill
                            style={{ objectFit: 'contain' }}
                            sizes="(max-width: 768px) 150px, 200px"
                            className="p-3"
                          />
                        </div>
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-teal">
                        {company.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <span className="text-gray-800 text-center font-medium text-lg mt-4">{company.name}</span>
                </motion.div>
              ))}
            </div>
            
            <div className="mt-16 text-center">
              <p className="text-gray-700 mb-8 text-lg max-w-3xl mx-auto">
                KIH invests in these companies to elevate their financial status and organizational impact in their respective fields of service.
              </p>
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-teal to-orange text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  const element = document.querySelector('#contact')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                Partner With Us
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Services
