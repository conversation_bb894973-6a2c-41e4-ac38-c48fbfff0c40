'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Building, Users, Target, Briefcase } from 'lucide-react'
import Image from 'next/image'

const Services = () => {
  const services = [
    {
      icon: TrendingUp,
      title: 'Project Funding',
      description: 'Providing funding for marginalized communities worldwide through prudent development and creative financing.',
      features: ['Non-Profit Support', 'Low-Income Initiatives', 'Project Structuring', 'Global Reach'],
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Building,
      title: 'Real Estate Development',
      description: 'Sustainable development projects that create jobs and improve communities.',
      features: ['Affordable Housing', 'Commercial Development', 'Urban Renewal', 'Sustainable Design', 'Visa/Residency Experts'],
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Mergers & Acquisitions',
      description: 'Strategic acquisitions to enhance business value and market position.',
      features: ['Due Diligence', 'Valuation Services', 'Deal Structuring', 'Integration Support'],
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Target,
      title: 'Humanitarian Initiatives',
      description: 'Projects focused on creating jobs, clean water, and solving housing problems.',
      features: ['Job Creation', 'Water Solutions', 'Housing Programs', 'Environmental Stewardship'],
      gradient: 'from-teal-light to-teal'
    },
    {
      icon: Briefcase,
      title: 'Business Development',
      description: 'Comprehensive solutions for new and growing businesses.',
      features: ['New Business Development', 'Market Entry Strategy', 'Business Planning', 'Operational Support'],
      gradient: 'from-purple-500 to-purple-700'
    },
    {
      icon: Briefcase,
      title: 'Commodities & Assets',
      description: 'Expert management and trading of commodities and business assets.',
      features: ['Commodities Trading', 'Asset Management', 'Portfolio Optimization', 'Strategic Planning'],
      gradient: 'from-indigo-500 to-indigo-700'
    }
  ]

  return (
    <section id="services" className="py-20 relative overflow-hidden">
      {/* Background with Bright Teal, Orange and Gold Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-teal-200 via-orange-200 to-yellow-200">
        <div className="absolute inset-0 bg-hero-pattern opacity-10"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-orange-400/30 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-teal-400/30 rounded-full blur-3xl floating" style={{ animationDelay: '3s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-yellow-400/20 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute top-10 left-1/4 w-48 h-48 bg-gold/25 rounded-full blur-2xl floating" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Our Humanitarian Services
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-orange-600 mx-auto mb-6"></div>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
            KIH is dedicated to empowering marginalized communities worldwide through strategic humanitarian investments. We provide comprehensive support and innovative financing solutions to drive sustainable development and create lasting impact.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white/20 backdrop-blur-md border border-white/30 p-8 rounded-2xl hover:bg-white/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden group">
                {/* Background Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-teal-400/20 to-orange-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Icon */}
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <h3 className="text-2xl font-semibold text-gray-800 mb-4">{service.title}</h3>
                  <p className="text-gray-700 mb-6 leading-relaxed">{service.description}</p>

                {/* Features */}
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: (index * 0.1) + (featureIndex * 0.05), duration: 0.4 }}
                      viewport={{ once: true }}
                      className="flex items-center text-gray-600 text-sm"
                    >
                      <div className="w-1.5 h-1.5 bg-gradient-to-r from-teal to-orange rounded-full mr-3"></div>
                      {feature}
                    </motion.li>
                  ))}
                </ul>

                </div>
                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-amber-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Legacy Aces Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="glass-dark p-8 rounded-2xl relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(245,158,11,0.1)_0%,transparent_70%)]"></div>
            
            <h3 className="text-3xl font-bold text-white mb-6 text-center relative z-10">
              Our Consortium: Legacy Aces
            </h3>
            <p className="text-white/90 mb-12 text-lg text-center max-w-4xl mx-auto relative z-10">
              Kaiteur International Holdings, LLC (KIH) is a dynamic conglomerate with a global network of partners. Our consortium of specialized companies works in harmony to advance our humanitarian mission, combining expertise in various sectors to create comprehensive solutions for underserved communities worldwide.
            </p>
            
            {/* Legacy Aces - Centered Above Others */}
            <div className="flex justify-center mb-12">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex flex-col items-center"
              >
                <div className="w-32 h-32 rounded-full bg-white shadow-lg border-2 border-gold overflow-hidden mb-4 hover:shadow-xl transition-all duration-300 p-4 flex items-center justify-center">
                  <div className="relative w-full h-full rounded-full overflow-hidden">
                    <Image
                      src="/businesslogos/legacyaces.png"
                      alt="Legacy Aces"
                      fill
                      style={{ objectFit: 'contain' }}
                      className="p-3"
                    />
                  </div>
                </div>
                <span className="text-white text-xl font-semibold text-center">Legacy Aces</span>
              </motion.div>
            </div>

            {/* Consortium Members Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-8">
              {[
                { name: 'Connoisseur', logo: '/businesslogos/connoisseur.png' },
                { name: 'Boriqn', logo: '/businesslogos/boriqn.png' },
                { name: 'KEP', logo: '/businesslogos/kep.png' },
                { name: 'Eighty-Three 50', logo: '/businesslogos/eightythree50.png' },
                { name: 'PierWater International', logo: '/businesslogos/pwi.png' },
                { name: 'Kingdom City', logo: '/businesslogos/kingdom.png' },
                { name: 'Astrid Capital', logo: '/businesslogos/astrid.png' },
                { name: 'Lemuria', logo: '/businesslogos/lemuria.png' }
              ].map((company, index) => (
                <motion.div
                  key={company.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center group"
                >
                  <div className="w-24 h-24 rounded-full bg-white/5 backdrop-blur-sm border border-white/10 flex items-center justify-center overflow-hidden transition-all duration-300 mb-4 hover:shadow-lg hover:scale-105 hover:border-amber-400/30 hover:bg-white/10 p-2">
                    {company.logo ? (
                      <div className="w-full h-full rounded-full overflow-hidden bg-white p-3">
                        <div className="relative w-full h-full">
                          <Image
                            src={company.logo}
                            alt={company.name}
                            fill
                            style={{ objectFit: 'contain' }}
                          />
                        </div>
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-teal">
                        {company.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <span className="text-white text-center font-medium text-sm mt-2">{company.name}</span>
                </motion.div>
              ))}
            </div>
            
            <div className="mt-16 text-center">
              <p className="text-white/80 mb-8 text-lg max-w-3xl mx-auto">
                KIH invests in these companies to elevate their financial status and organizational impact in their respective fields of service.
              </p>
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-teal to-orange text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  const element = document.querySelector('#contact')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                Partner With Us
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Services
