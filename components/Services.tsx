'use client'

import { motion } from 'framer-motion'
import { TrendingUp, Building, Users, Target, Briefcase } from 'lucide-react'
import Image from 'next/image'

const Services = () => {
  // Background gradient classes for alternating sections
  const sectionBackgrounds = [
    'bg-gradient-to-r from-teal-700 via-teal-600 to-amber-600',
    'bg-gradient-to-r from-amber-600 via-amber-500 to-amber-400',
    'bg-gradient-to-r from-teal-600 via-teal-500 to-amber-500'
  ];
  const services = [
    {
      icon: TrendingUp,
      title: 'Project Funding',
      description: 'Providing funding for marginalized communities worldwide through prudent development and creative financing.',
      features: ['Non-Profit Support', 'Low-Income Initiatives', 'Project Structuring', 'Global Reach'],
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Building,
      title: 'Real Estate Development',
      description: 'Sustainable development projects that create jobs and improve communities.',
      features: ['Affordable Housing', 'Commercial Development', 'Urban Renewal', 'Sustainable Design', 'Visa/Residency Experts'],
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Mergers & Acquisitions',
      description: 'Strategic acquisitions to enhance business value and market position.',
      features: ['Due Diligence', 'Valuation Services', 'Deal Structuring', 'Integration Support'],
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Target,
      title: 'Humanitarian Initiatives',
      description: 'Projects focused on creating jobs, clean water, and solving housing problems.',
      features: ['Job Creation', 'Water Solutions', 'Housing Programs', 'Environmental Stewardship'],
      gradient: 'from-teal-light to-teal'
    },
    {
      icon: Briefcase,
      title: 'Business Development',
      description: 'Comprehensive solutions for new and growing businesses.',
      features: ['New Business Development', 'Market Entry Strategy', 'Business Planning', 'Operational Support'],
      gradient: 'from-purple-500 to-purple-700'
    },
    {
      icon: Briefcase,
      title: 'Commodities & Assets',
      description: 'Expert management and trading of commodities and business assets.',
      features: ['Commodities Trading', 'Asset Management', 'Portfolio Optimization', 'Strategic Planning'],
      gradient: 'from-indigo-500 to-indigo-700'
    }
  ]

  return (
    <section id="services" className="py-20 relative overflow-hidden">
      {/* Background Gradients */}
      <div className="absolute inset-0 -z-10">
        <div className={`absolute inset-0 ${sectionBackgrounds[0]} opacity-20`}></div>
        <div className="absolute inset-0 bg-[url('/images/grid.svg')] opacity-5"></div>
      </div>
      {/* Background with Teal and Gold Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-teal-900 via-slate-900 to-amber-900">
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-amber-500/10 rounded-full blur-3xl floating"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-teal-500/10 rounded-full blur-3xl floating" style={{ animationDelay: '3s' }}></div>
        <div className="absolute top-1/2 left-1/2 w-1/2 h-1/2 bg-gradient-radial from-amber-400/5 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our Humanitarian Services
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-amber-400 to-amber-600 mx-auto mb-6"></div>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            KIH is dedicated to empowering marginalized communities worldwide through strategic humanitarian investments. We provide comprehensive support and innovative financing solutions to drive sustainable development and create lasting impact.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="glass-dark p-8 rounded-2xl hover-lift h-full relative overflow-hidden group">
                {/* Background Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-teal-900/50 to-amber-900/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                {/* Icon */}
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <h3 className="text-2xl font-semibold text-white mb-4">{service.title}</h3>
                  <p className="text-white/80 mb-6 leading-relaxed">{service.description}</p>

                {/* Features */}
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: (index * 0.1) + (featureIndex * 0.05), duration: 0.4 }}
                      viewport={{ once: true }}
                      className="flex items-center text-white/60 text-sm"
                    >
                      <div className="w-1.5 h-1.5 bg-gradient-to-r from-teal to-orange rounded-full mr-3"></div>
                      {feature}
                    </motion.li>
                  ))}
                </ul>

                </div>
                {/* Hover Effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-amber-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Legacy Aces Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="glass-dark p-8 rounded-2xl relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(245,158,11,0.1)_0%,transparent_70%)]"></div>
            
            <h3 className="text-3xl font-bold text-white mb-6 text-center relative z-10">
              Our Consortium: Legacy Aces
            </h3>
            <p className="text-white/90 mb-12 text-lg text-center max-w-4xl mx-auto relative z-10">
              Kaiteur International Holdings, LLC (KIH) is a dynamic conglomerate with a global network of partners. Our consortium of specialized companies works in harmony to advance our humanitarian mission, combining expertise in various sectors to create comprehensive solutions for underserved communities worldwide.
            </p>
            
            {/* Legacy Aces - Centered Above Others */}
            <div className="flex justify-center mb-12">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="flex flex-col items-center"
              >
                <div className="w-32 h-32 rounded-full bg-white shadow-lg border-2 border-gold flex items-center justify-center overflow-hidden mb-4 hover:shadow-xl transition-all duration-300 p-4">
                  <div className="w-full h-full rounded-full bg-white flex items-center justify-center">
                    <Image
                      src="/businesslogos/legacyaces.png"
                      alt="Legacy Aces"
                      width={128}
                      height={128}
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>
                <span className="text-white text-xl font-semibold text-center">Legacy Aces</span>
              </motion.div>
            </div>

            {/* Consortium Members Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-8">
              {[
                { name: 'Connoisseur', logo: '/businesslogos/connoisseur.png' },
                { name: 'Boriqn', logo: '/businesslogos/boriqn.png' },
                { name: 'KEP', logo: '/businesslogos/kep.png' },
                { name: 'Eighty-Three 50', logo: '/businesslogos/eightythree50.png' },
                { name: 'PierWater International', logo: '/businesslogos/pwi.png' },
                { name: 'Kingdom City', logo: '/businesslogos/kingdom.png' },
                { name: 'Astrid Capital', logo: '/businesslogos/astrid.png' },
                { name: 'Lemuria', logo: '/businesslogos/lemuria.png' }
              ].map((company, index) => (
                <motion.div
                  key={company.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  viewport={{ once: true }}
                  className="flex flex-col items-center group"
                >
                  <div className="w-24 h-24 rounded-full bg-white/5 backdrop-blur-sm border border-white/10 flex items-center justify-center overflow-hidden transition-all duration-300 mb-4 hover:shadow-lg hover:scale-105 hover:border-amber-400/30 hover:bg-white/10 p-2">
                    {company.logo ? (
                      <div className="w-full h-full rounded-full bg-white flex items-center justify-center p-2">
                        <Image
                          src={company.logo}
                          alt={company.name}
                          width={96}
                          height={96}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-teal">
                        {company.name.charAt(0)}
                      </span>
                    )}
                  </div>
                  <span className="text-white text-center font-medium text-sm mt-2">{company.name}</span>
                </motion.div>
              ))}
            </div>
            
            <div className="mt-16 text-center">
              <p className="text-white/80 mb-8 text-lg max-w-3xl mx-auto">
                KIH invests in these companies to elevate their financial status and organizational impact in their respective fields of service.
              </p>
              <motion.button
                className="px-8 py-4 bg-gradient-to-r from-teal to-orange text-white font-semibold rounded-full shadow-xl hover:shadow-2xl transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  const element = document.querySelector('#contact')
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' })
                  }
                }}
              >
                Partner With Us
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Services
