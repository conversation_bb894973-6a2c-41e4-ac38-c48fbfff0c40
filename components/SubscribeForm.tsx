'use client'

import { useForm } from '@formspree/react'
import { motion } from 'framer-motion'
import { useState } from 'react'

export default function SubscribeForm() {
  const [state, handleSubmit] = useForm("xnnvwenq")
  const [email, setEmail] = useState('')

  if (state.succeeded) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-teal-300 text-sm mt-2"
      >
        Thank you for subscribing!
      </motion.div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <input type="hidden" name="subject" value="New Subscription" />
      <div>
        <label htmlFor="email" className="sr-only">Email address</label>
        <input
          id="email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          placeholder="Enter your email"
        />
      </div>
      <motion.button
        type="submit"
        disabled={state.submitting}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full px-6 py-3 bg-gradient-to-r from-teal-600 to-teal-500 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300"
      >
        {state.submitting ? 'Subscribing...' : 'Subscribe'}
      </motion.button>
    </form>
  )
}
