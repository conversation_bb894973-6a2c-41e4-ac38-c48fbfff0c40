'use client'

import { motion } from 'framer-motion'
import { Globe, Zap, Users, Clock } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Serving clients and partners across multiple continents with a focus on emerging markets.',
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Zap,
      title: 'Financial Solutions',
      description: 'Providing innovative funding and resource solutions for underserved communities and businesses.',
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Community Focus',
      description: 'Dedicated to empowering marginalized communities through strategic financial support.',
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Clock,
      title: 'Proven Experience',
      description: '25 years of developing strategies to provide resources to domestic and international entities.',
      gradient: 'from-teal-light to-teal'
    }
  ]

  return (
    <div id="about" className="py-24 relative overflow-hidden bg-white">
      {/* Subtle Gradient Background */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-teal-500 to-orange-500"></div>
        <div className="absolute top-1/4 -right-40 w-96 h-96 bg-teal-100/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 -left-40 w-96 h-96 bg-orange-100/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2 className="text-4xl md:text-5xl font-bold text-center mb-6 text-black">
            About Kaiteur International
          </motion.h2>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-teal-600 mx-auto mb-6"></div>
          <motion.p className="text-xl text-center max-w-3xl mx-auto text-gray-800 mb-12">
            Kaiteur International Holdings (KIH) is a private investment and management holding company with twenty-five years of developing strategies to provide resources to both domestic and international partners.
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="p-6 rounded-xl bg-white border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 h-full flex flex-col hover:border-teal-100 hover:bg-gray-50/50">
              <h3 className="text-2xl font-semibold text-black mb-4">Our Mission & Vision</h3>
              <p className="text-gray-800 mb-4 leading-relaxed">
                <span className="font-semibold text-teal-600">Vision:</span> "Our Plan Your Future". Our vision is to empower communities throughout this nation and the world with financial resources to think big, to want more and to obtain their dreams.
              </p>
              <p className="text-gray-800 mb-4 leading-relaxed">
                <span className="font-semibold text-teal-600">Mission:</span> Our mission is to empower communities and underserved areas by providing more opportunities and access for funding needed for their dreams to be achieved through our partners.
              </p>
              <p className="text-gray-800 leading-relaxed">
                We exist to strengthen the financial well-being of marginalized and underserved communities, businesses, and organizations by providing funding and resources to these groups.
              </p>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-white/30 backdrop-blur-md border border-white/40 p-6 rounded-xl hover:bg-white/40 hover:shadow-xl transition-all duration-300 group"
                whileHover={{ scale: 1.02 }}
              >
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 text-white ${
                  feature.title === 'Global Reach' || feature.title === 'Proven Experience' 
                    ? 'bg-teal-500' 
                    : 'bg-orange-500'
                }`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-800 mb-2">{feature.title}</h4>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Company History */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-800 mb-8">Our Story</h3>
          <div className="bg-white/20 backdrop-blur-md border border-white/30 p-6 rounded-2xl shadow-lg hover:bg-white/30 hover:shadow-xl transition-all duration-300">
            <h4 className="text-2xl font-semibold text-gray-800 mb-4">Company History</h4>
            <p className="text-gray-700 mb-4 leading-relaxed">
              Kaiteur was founded by Ien and Arlene Chapman, a dynamic husband and wife team with a shared vision for humanitarian impact. Ien, a veteran who served his country with honor, recognized the need to support both former service members and underserved communities. Arlene, with her deep commitment to social causes, has been instrumental in shaping the company's humanitarian focus. Together, they have built a legacy that will endure for generations.
            </p>
            <p className="text-gray-700 leading-relaxed">
              What began as a family business dedicated to helping their local community has grown into an international force for good. Today, Kaiteur is proud to be a certified Service-Disabled Veteran-Owned Small Business (SDVOSB) and a certified 8(a) business, allowing us to expand our impact. Headquartered in Charlotte, North Carolina, we are actively expanding our global presence with upcoming offices in Africa, Puerto Rico, and London to better serve communities worldwide.
            </p>
          </div>
        </motion.div>

        {/* Our Approach */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-800 mb-8">Our Approach</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'The Opportunity', 
                description: 'KIH was formed to help solve funding challenges for non-profit organizations. Key performance indicators and stringent analysis of each non-profit will be applied. KIH will encourage and support business leaders as they change their paradigm in the financial interface while making it easier to accomplish their goals.' 
              },
              { 
                title: 'Our Solution', 
                description: 'Our process of funding involves looking at cities that are underserved and underdeveloped. KIH will use a proven process to create funding plans, and work with individuals and groups step by step to accomplish their goals.' 
              },
              { 
                title: 'What Makes Us Different', 
                description: 'KIH is different in that most holding companies put more value on the ROI than the number of people served. We are focused on the people and how we can help them individually to create successful programs and non-profits.' 
              }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-white/20 backdrop-blur-md border border-white/30 p-6 rounded-xl shadow-md hover:bg-white/30 hover:shadow-lg transition-all duration-300 h-full flex flex-col"
                whileHover={{ y: -5 }}
              >
                <h4 className="text-xl font-semibold text-gray-800 mb-3">{value.title}</h4>
                <p className="text-gray-700 text-sm flex-grow">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Leadership Section */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Leadership Team</h2>
          <div className="w-20 h-1 bg-gradient-to-r from-orange-500 to-teal-600 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto">
            The visionary leaders guiding our humanitarian mission forward.
          </p>
        </motion.div>

        {/* Founders Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {/* Ien Chapman */}
          <motion.div 
            className="bg-white/20 backdrop-blur-md border border-white/30 p-8 rounded-xl shadow-lg hover:bg-white/30 hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-2">Ien Chapman</h3>
            <p className="text-orange-600 font-medium text-lg mb-4">Co-Founder & CEO</p>
            <p className="text-gray-700">Veteran | Entrepreneur | Visionary</p>
            <p className="text-gray-700 mt-4 text-sm">
              A decorated veteran with a passion for serving others, Ien brings strategic leadership and a commitment to excellence in all our humanitarian initiatives.
            </p>
          </motion.div>

          {/* Arlene Chapman */}
          <motion.div 
            className="bg-white/20 backdrop-blur-md border border-white/30 p-8 rounded-xl shadow-lg hover:bg-white/30 hover:shadow-xl transition-all duration-300"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -5 }}
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-2">Arlene Chapman</h3>
            <p className="text-orange-600 font-medium text-lg mb-4">Co-Founder</p>
            <p className="text-gray-700">Community Leader | Strategist | Philanthropist</p>
            <p className="text-gray-700 mt-4 text-sm">
              With a heart for service and a mind for business, Arlene ensures our operations align with our mission to create lasting, positive change in communities worldwide.
            </p>
          </motion.div>
        </div>

        {/* C-Team Section */}
        <motion.div 
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Executive Leadership</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'Wayne Robinson', role: 'Chief Operating Officer' },
              { name: 'Xavier Grier', role: 'Chief Marketing Officer' },
              { name: 'Johnny Walker', role: 'Chief Human Resources Officer' },
              { name: 'Joel Rodriguez', role: 'Chief Information Officer' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-white/20 backdrop-blur-md border border-white/30 p-6 rounded-xl shadow-md hover:bg-white/30 hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <h4 className="text-lg font-semibold text-black mb-1">{member.name}</h4>
                <p className="text-teal-600 text-sm font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Directors Section */}
        <motion.div 
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">Directors</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-3xl mx-auto">
            {[
              { name: 'Mary Young', role: 'Sr. Executive Director' },
              { name: 'Alfred Strachan', role: 'Jr. Executive Director' }
            ].map((member, index) => (
              <motion.div
                key={member.name}
                className="bg-white/20 backdrop-blur-md border border-white/30 p-4 rounded-xl shadow-md hover:bg-white/30 hover:shadow-lg transition-all duration-300 text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                viewport={{ once: true }}
                whileHover={{ y: -3 }}
              >
                <h4 className="text-md font-semibold text-black">{member.name}</h4>
                <p className="text-teal-600 text-xs font-medium">{member.role}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Consortium Legacy */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-2xl font-bold text-slate-800 mb-6">Our Consortium Legacy</h3>
          <p className="text-slate-600 max-w-3xl mx-auto">
            Through our consortium, we bring together a network of experts and organizations committed to making a difference in communities worldwide.
          </p>
        </motion.div>
      </div>
    </div>
  )
}

export default About
