'use client'

import { motion } from 'framer-motion'
import { Globe, Zap, Users, Clock } from 'lucide-react'

const About = () => {
  const features = [
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Serving clients and partners across multiple continents with a focus on emerging markets.',
      gradient: 'from-teal to-teal-dark'
    },
    {
      icon: Zap,
      title: 'Financial Solutions',
      description: 'Providing innovative funding and resource solutions for underserved communities and businesses.',
      gradient: 'from-orange to-orange-dark'
    },
    {
      icon: Users,
      title: 'Community Focus',
      description: 'Dedicated to empowering marginalized communities through strategic financial support.',
      gradient: 'from-gold to-yellow-600'
    },
    {
      icon: Clock,
      title: 'Proven Experience',
      description: '25 years of developing strategies to provide resources to domestic and international entities.',
      gradient: 'from-teal-light to-teal'
    }
  ]

  return (
    <section id="about" className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white via-slate-50 to-teal-50">
        <div className="absolute top-0 left-0 w-full h-full opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 bg-teal rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange rounded-full blur-3xl"></div>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-dark mb-4">
            About Kaiteur International
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-teal to-orange mx-auto mb-6"></div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Kaiteur International Holdings (KIH) is a privately held management holding company with twenty-five years of developing strategies to provide resources to both domestic and international entities.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Story Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="glass p-8 rounded-2xl hover-lift">
              <h3 className="text-2xl font-semibold text-teal mb-4">Our Mission & Vision</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                <span className="font-semibold">Vision:</span> &quot;Our Plan Your Future&quot;. Our vision is to empower communities throughout this nation and the world with financial resources to think big, to want more and to obtain their dreams.
              </p>
              <p className="text-gray-600 mb-4 leading-relaxed">
                <span className="font-semibold">Mission:</span> Our mission is to empower communities and the people in them to think big, want more, and give them the access to funding needed for their dreams to become reality. Through our partners this dream is possible.
              </p>
              <p className="text-gray-600 leading-relaxed">
                We exist to strengthen the financial well-being of marginalized and underserved communities, businesses, and organizations by providing funding and resources to these groups.
              </p>
            </div>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="glass p-6 rounded-xl hover-lift group"
                whileHover={{ scale: 1.02 }}
              >
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-gray-dark mb-2">{feature.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Company History */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-dark mb-8">Our Story</h3>
          <div className="glass p-8 rounded-2xl hover-lift">
            <h4 className="text-2xl font-semibold text-teal mb-4">Company History</h4>
            <p className="text-gray-600 mb-4 leading-relaxed">
              Kaiteur was founded by Ien and Arlene Chapman. Both having served the community they have lived in for over 30 years. Ien served his country and always knew more needed to be done to help his former service members. The two of them have created a company that will last generations, eventually being passed to their children.
            </p>
            <p className="text-gray-600 mb-4 leading-relaxed">
              Kaiteur was created as a family business to not only help the Chapman family around the world but also the communities the family lives in. Currently, Kaiteur is headquartered in Charlotte, North Carolina with offices in Africa, the Dominican Republic, and London.
            </p>
          </div>
        </motion.div>

        {/* Our Approach */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-dark mb-8">Our Approach</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { 
                title: 'The Opportunity', 
                description: 'KIH was formed to help solve funding challenges for non-profit organizations. Key performance indicators and stringent analysis of each non-profit will be applied. KIH will encourage and support business leaders as they change their paradigm in the financial interface while making it easier to accomplish their goals.' 
              },
              { 
                title: 'Our Solution', 
                description: 'Our process of funding involves looking at cities that are underserved and underdeveloped. KIH will use a proven process to create funding plans, and work with individuals and groups step by step to accomplish their goals.' 
              },
              { 
                title: 'What Makes Us Different', 
                description: 'KIH is different in that most holding companies put more value on the ROI than the number of people served. We are focused on the people and how we can help them individually to create successful programs and non-profits.' 
              }
            ].map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                className="glass p-6 rounded-xl hover-lift h-full flex flex-col"
                whileHover={{ y: -5 }}
              >
                <h4 className="text-xl font-semibold text-teal mb-3">{value.title}</h4>
                <p className="text-gray-600 text-sm flex-grow">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Team Section */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Leadership Team</h2>
          <div className="w-20 h-1 bg-teal-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            The dedicated professionals driving our vision forward.
          </p>
        </motion.div>

        <div className="space-y-8">
          {/* CEO Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">Chief Executive Officer</h3>
            <div className="text-center">
              <p className="text-lg font-medium text-gray-900">Ien Chapman</p>
              <p className="text-teal-600">Founder & CEO</p>
            </div>
          </div>

          {/* C-Team Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-800">Executive Team</h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
              {[
                { name: 'Wayne Robinson', role: 'Chief Operating Officer' },
                { name: 'Xavier Grier', role: 'Chief Marketing Officer' },
                { name: 'Johnny Walker', role: 'Chief Human Resources Officer' },
                { name: 'Joel Rodriguez', role: 'Chief Information Officer' }
              ].map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                  viewport={{ once: true }}
                  className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <p className="font-medium text-gray-900 text-center">{member.name}</p>
                  <p className="text-sm text-teal-600 text-center">{member.role}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Directors Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gray-50 px-6 py-3 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-800">Directors</h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4">
              {[
                { name: 'Mary Young', role: 'Executive Director' },
                { name: 'Alfred Strachan', role: 'Senior Director' }
              ].map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                  viewport={{ once: true }}
                  className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <p className="font-medium text-gray-900 text-center">{member.name}</p>
                  <p className="text-sm text-teal-600 text-center">{member.role}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
