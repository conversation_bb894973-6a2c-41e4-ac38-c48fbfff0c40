/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
    ],
    unoptimized: true, // Required for static export
  },
  output: 'export', // Static site generation
  trailingSlash: true, // Ensure consistent URLs
  // Webpack configuration
  webpack: (config, { isServer, dev }) => {
    // Important: return the modified config
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      module: false,
      dns: 'mock',
      child_process: false,
      dgram: false,
      http2: false,
      process: false,
    };

    // Fixes npm packages that depend on `module` field
    config.resolve.mainFields = ['browser', 'module', 'main'];

    // Add rule to handle TypeScript files
    config.module.rules.push({
      test: /\.(ts|tsx)$/,
      use: [
        {
          loader: 'ts-loader',
          options: {
            transpileOnly: true,
          },
        },
      ],
    });

    return config;
  },
  // Next.js 14 has server actions enabled by default
};

module.exports = nextConfig;
